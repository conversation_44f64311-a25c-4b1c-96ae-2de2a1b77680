namespace GMCadiomJsonProvider.Extensions;

/// <summary>
/// Extension methods for ISignalRServerBuilder to integrate GMCadiomJsonProvider with ASP.NET Core SignalR.
/// </summary>
public static class SignalRBuilderExtensions
{
    /// <summary>
    /// Configures SignalR to use GMCadiomJsonProvider with the default Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The ISignalRServerBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The ISignalRServerBuilder for method chaining.</returns>
    public static ISignalRServerBuilder AddGMCadiomJsonProtocol(this ISignalRServerBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJsonProtocol(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Configures SignalR to use GMCadiomJsonProvider with the specified provider type.
    /// </summary>
    /// <param name="builder">The ISignalRServerBuilder instance.</param>
    /// <param name="providerType">The JSON provider type to use.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The ISignalRServerBuilder for method chaining.</returns>
    public static ISignalRServerBuilder AddGMCadiomJsonProtocol(this ISignalRServerBuilder builder, JsonProviderType providerType, Action<JsonConfiguration>? configure = null)
    {
        var config = new JsonConfiguration { ProviderType = providerType };
        configure?.Invoke(config);

        // Register the JSON provider
        builder.Services.AddJsonProvider(config);

        // Configure SignalR JSON protocol based on provider type
        if (providerType == JsonProviderType.Microsoft)
        {
            builder.AddJsonProtocol(options =>
            {
                var systemTextJsonOptions = config.ToSystemTextJsonOptions();
                options.PayloadSerializerOptions.WriteIndented = systemTextJsonOptions.WriteIndented;
                options.PayloadSerializerOptions.PropertyNameCaseInsensitive = systemTextJsonOptions.PropertyNameCaseInsensitive;
                options.PayloadSerializerOptions.AllowTrailingCommas = systemTextJsonOptions.AllowTrailingCommas;
                options.PayloadSerializerOptions.DefaultIgnoreCondition = systemTextJsonOptions.DefaultIgnoreCondition;
                options.PayloadSerializerOptions.PropertyNamingPolicy = systemTextJsonOptions.PropertyNamingPolicy;
                options.PayloadSerializerOptions.DictionaryKeyPolicy = systemTextJsonOptions.DictionaryKeyPolicy;
                options.PayloadSerializerOptions.IncludeFields = systemTextJsonOptions.IncludeFields;
                options.PayloadSerializerOptions.MaxDepth = systemTextJsonOptions.MaxDepth;
                options.PayloadSerializerOptions.ReadCommentHandling = systemTextJsonOptions.ReadCommentHandling;
                options.PayloadSerializerOptions.NumberHandling = systemTextJsonOptions.NumberHandling;
                options.PayloadSerializerOptions.Encoder = systemTextJsonOptions.Encoder;
            });
        }
        else if (providerType == JsonProviderType.Newtonsoft)
        {
            builder.AddNewtonsoftJsonProtocol(options =>
            {
                var newtonsoftSettings = config.ToNewtonsoftJsonSettings();
                options.PayloadSerializerSettings.Formatting = newtonsoftSettings.Formatting;
                options.PayloadSerializerSettings.NullValueHandling = newtonsoftSettings.NullValueHandling;
                options.PayloadSerializerSettings.DateFormatHandling = newtonsoftSettings.DateFormatHandling;
                options.PayloadSerializerSettings.MissingMemberHandling = newtonsoftSettings.MissingMemberHandling;
                options.PayloadSerializerSettings.ReferenceLoopHandling = newtonsoftSettings.ReferenceLoopHandling;
                options.PayloadSerializerSettings.ContractResolver = newtonsoftSettings.ContractResolver;
                options.PayloadSerializerSettings.Culture = newtonsoftSettings.Culture;
                options.PayloadSerializerSettings.MaxDepth = newtonsoftSettings.MaxDepth;
            });
        }

        return builder;
    }

    /// <summary>
    /// Configures SignalR to use GMCadiomJsonProvider with Microsoft System.Text.Json provider.
    /// </summary>
    /// <param name="builder">The ISignalRServerBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The ISignalRServerBuilder for method chaining.</returns>
    public static ISignalRServerBuilder AddGMCadiomMicrosoftJsonProtocol(this ISignalRServerBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJsonProtocol(JsonProviderType.Microsoft, configure);
    }

    /// <summary>
    /// Configures SignalR to use GMCadiomJsonProvider with Newtonsoft.Json provider.
    /// </summary>
    /// <param name="builder">The ISignalRServerBuilder instance.</param>
    /// <param name="configure">Optional action to configure the JSON configuration.</param>
    /// <returns>The ISignalRServerBuilder for method chaining.</returns>
    public static ISignalRServerBuilder AddGMCadiomNewtonsoftJsonProtocol(this ISignalRServerBuilder builder, Action<JsonConfiguration>? configure = null)
    {
        return builder.AddGMCadiomJsonProtocol(JsonProviderType.Newtonsoft, configure);
    }

    /// <summary>
    /// Configures SignalR to use GMCadiomJsonProvider using a fluent builder pattern.
    /// </summary>
    /// <param name="builder">The ISignalRServerBuilder instance.</param>
    /// <param name="configure">Action to configure the JSON provider using the builder.</param>
    /// <returns>The ISignalRServerBuilder for method chaining.</returns>
    public static ISignalRServerBuilder AddGMCadiomJsonProtocol(this ISignalRServerBuilder builder, Action<IJsonProviderBuilder> configure)
    {
        var jsonBuilder = new JsonProviderBuilder();
        configure(jsonBuilder);
        var provider = jsonBuilder.Build();

        // Register the built provider
        builder.Services.AddSingleton<IJsonProvider>(provider);

        // Get the configuration from the provider to configure SignalR
        var config = GetConfigurationFromProvider(provider);

        return builder.AddGMCadiomJsonProtocol(config.ProviderType, _ => { });
    }

    /// <summary>
    /// Configures SignalR to use GMCadiomJsonProvider from a JSON configuration string.
    /// </summary>
    /// <param name="builder">The ISignalRServerBuilder instance.</param>
    /// <param name="jsonConfig">The JSON configuration string.</param>
    /// <returns>The ISignalRServerBuilder for method chaining.</returns>
    public static ISignalRServerBuilder AddGMCadiomJsonProtocolFromConfig(this ISignalRServerBuilder builder, string jsonConfig)
    {
        var config = JsonConfiguration.FromJson(jsonConfig);
        return builder.AddGMCadiomJsonProtocol(config.ProviderType, _ => { });
    }

    /// <summary>
    /// Extracts configuration from a provider instance.
    /// This is a helper method to get configuration details from an existing provider.
    /// </summary>
    private static JsonConfiguration GetConfigurationFromProvider(IJsonProvider provider)
    {
        // Since we can't directly access the internal configuration,
        // we'll create a default configuration with the provider type
        return new JsonConfiguration
        {
            ProviderType = provider.ProviderType
        };
    }
}
